{"name": "@distube/ytdl-core", "description": "DisTube fork of ytdl-core. YouTube video downloader in pure javascript.", "keywords": ["youtube", "video", "download", "distube"], "version": "4.14.4", "repository": {"type": "git", "url": "git://github.com/distubejs/ytdl-core.git"}, "author": "Skick (https://github.com/skick1234)", "contributors": ["fent <<EMAIL>> (https://github.com/fent)", "<PERSON> (https://github.com/TimeForANinja)", "<PERSON> (https://github.com/andrewrk)", "<PERSON><PERSON><PERSON> (https://github.com/mallendeo)", "<PERSON> (https://github.com/raltamirano)", "<PERSON> (https://github.com/<PERSON><PERSON>)", "<PERSON><PERSON><PERSON> (https://github.com/Roki100)", "<PERSON> (https://github.com/Million900o)"], "main": "./lib/index.js", "types": "./typings/index.d.ts", "files": ["lib", "typings"], "dependencies": {"http-cookie-agent": "^6.0.5", "m3u8stream": "^0.8.6", "miniget": "^4.2.3", "sax": "^1.4.1", "tough-cookie": "^4.1.4", "undici": "five"}, "devDependencies": {"@types/node": "^22.1.0", "eslint": "^8.57.0", "typescript": "^5.5.4"}, "engines": {"node": ">=14.0"}, "license": "MIT", "funding": "https://github.com/distubejs/ytdl-core?sponsor", "scripts": {"lint": "eslint ./", "lint:fix": "eslint --fix ./", "lint:typings": "tslint typings/index.d.ts", "lint:typings:fix": "tslint --fix typings/index.d.ts"}}