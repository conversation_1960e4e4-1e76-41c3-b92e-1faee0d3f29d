const fs = require('fs');
const { owners, emco, logChannelId } = require(`${process.cwd()}/config`);
const { exec } = require('child_process');
const { MessageEmbed } = require('discord.js');
const ms = require('ms');
const crypto = require('crypto');
const schedule = require('node-schedule');

module.exports = {
  name: 'addsub',
  run: async (client, message, args) => {
    if (!owners.includes(message.author.id)) return;
    if (message.author.bot) return;

    const mention = message.mentions.members.first();
    if (!mention) return message.reply("**يرجي إرفاق منشن الشخص.**");

    const userId = mention.id;
    const serverId = args[1];
    if (!serverId) return message.reply("**يرجي إرفاق ايدي السيرفر.**");

    let bots = [];
    try {
      const data = fs.readFileSync('./bots.json', 'utf8');
      bots = JSON.parse(data);
    } catch (error) {
      console.error('❌>', error);
    }

    const count = parseInt(args[2]);
    if (!count || count <= 0 || count > bots.length) {
      return message.reply('**يرجي إرفاق عدد البوتات.**');
    }

    const subscriptionTime = args[3];
    const subscriptionDuration = ms(subscriptionTime);
    if (!subscriptionDuration) return message.reply("**يرجى إرفاق وقت صحيح للاشتراك.**");

    const expirationTime = Date.now() + subscriptionDuration;
    const randomCode = generateRandomCode(5);

    // Save subscription data
    const logsData = {
      user: userId,
      server: serverId,
      botsCount: count,
      subscriptionTime: subscriptionTime,
      expirationTime: expirationTime,
      code: `#${randomCode}`,
      notified: false // Track if 2-day notification was sent
    };

    try {
      let logsArray = [];
      
      // التحقق من وجود الملف
      if (fs.existsSync('./logs.json')) {
        const logs = fs.readFileSync('./logs.json', 'utf8');
        // التحقق من أن الملف ليس فارغًا
        if (logs && logs.trim() !== '') {
          try {
            logsArray = JSON.parse(logs);
            if (!Array.isArray(logsArray)) {
              logsArray = [];
            }
          } catch (parseError) {
            console.error('❌> خطأ في تحليل ملف logs.json:', parseError);
            logsArray = [];
          }
        }
      }
      
      logsArray.push(logsData);
      fs.writeFileSync('./logs.json', JSON.stringify(logsArray, null, 2));
      console.log(`تم حفظ اشتراك جديد برمز: #${randomCode}`);
    } catch (error) {
      console.error('❌> حدث خطأ أثناء حفظ بيانات الاشتراك:', error);
      message.reply("**حدث خطأ أثناء حفظ بيانات الاشتراك. الرجاء المحاولة مرة أخرى.**");
      return;
    }

    // Update tokens.json
    const givenTokens = bots.splice(0, count);
    let tokens = [];
    try {
      const tokensData = fs.readFileSync('./tokens.json', 'utf8');
      tokens = JSON.parse(tokensData);
      if (!Array.isArray(tokens)) {
        tokens = [];
      }
    } catch (error) {
      console.error('حدث خطأ أثناء قراءة الملف tokens.json:', error);
    }

    givenTokens.forEach(token => {
      tokens.push({
        token: token.token,
        Server: serverId,
        channel: null,
        chat: null,
        status: null,
        client: userId,
        useEmbeds: false,
        code: `#${randomCode}`
      });
    });

    fs.writeFileSync('./tokens.json', JSON.stringify(tokens, null, 2));
    fs.writeFileSync('./bots.json', JSON.stringify(bots, null, 2));

    // Schedule notifications
    scheduleExpirationNotifications(client, userId, expirationTime, randomCode, count, serverId);

    // Restart bot
    exec('pm2 restart index.js', (error, stdout, stderr) => {
      if (error) {
        console.error(`❌> ${error}`);
        return;
      }
      console.log(`❌> ${stdout}`);
      console.log(`❌> ${stderr}`);
    });

    await message.react('✅');

    // Send subscription details to user
    try {
      const serialNumber = `TBP-FATF-${Date.now()}`;
      const referenceNumber = Math.floor(Math.random() * 1000000000000000000);
      
      const userEmbed = new MessageEmbed()
        .setTitle('Oryn Store | تفاصيل الاشتراك')
        .setColor('#355a5d')
        .setDescription(`
          **${mention.user.username}** تم تفعيل اشتراكك بنجاح!
          
          **المنتج:** Music
          **الرمز:** ${randomCode}
          **الرقم التسلسلي:** ${serialNumber}
          **الرقم المرجعي:** ${referenceNumber}
          **تاريخ الانتهاء:** ${new Date(expirationTime).toLocaleDateString()}
          **العدد:** ${count} bots
          **Oryn Store**
        `)
        .setThumbnail('https://media.discordapp.net/attachments/1334220115788562455/1356380279211692162/Logo.png?ex=67eefe12&is=67edac92&hm=cb96012b44392aa4acbb0aefb9f12de4f24b581fa976353ca44f0137036ea617&=&format=webp&quality=lossless&width=689&height=689')
        .setFooter({ text: 'في حال حدثت أي مشكلة بالبوتات، يرجى التواصل مع الدعم الفني لاورين ستور، شكراً لاستخدامك خدماتنا' });

      await mention.send({ 
        files: ['https://i.ibb.co/LzgvGT9Q/1.png'],
        embeds: [userEmbed] 
      });
    } catch (error) {
      console.error('فشل في إرسال التفاصيل للمستخدم:', error);
    }

    // Send log embed
    const logChannel = client.channels.cache.get(logChannelId);
    if (logChannel) {
      const embed = new MessageEmbed()
        .setTitle('Oryn Store | تفاصيل الاشتراك الجديد')
        .setColor('#355a5d')
        .addFields(
          { name: 'المشرف', value: `${message.author.tag} (${message.author.id})`, inline: true },
          { name: 'المستخدم', value: `${mention.user.tag} (${userId})`, inline: true },
          { name: 'السيرفر', value: serverId, inline: true },
          { name: 'عدد البوتات', value: count.toString(), inline: true },
          { name: 'مدة الاشتراك', value: subscriptionTime, inline: true },
          { name: 'تاريخ الانتهاء', value: new Date(expirationTime).toLocaleString(), inline: true },
          { name: 'الكود', value: `#${randomCode}`, inline: true }
        )
        .setThumbnail('https://cdn.discordapp.com/attachments/1091536665912299530/1198775903480717322/add.png')
        .setTimestamp();

      await logChannel.send({ embeds: [embed] });
    }
  }
};

function generateRandomCode(length) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let code = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    code += characters.charAt(randomIndex);
  }
  return code;
}

function scheduleExpirationNotifications(client, userId, expirationTime, code, count, serverId) {
  // Schedule 2-day warning notification
  const twoDaysBefore = new Date(expirationTime - 2 * 24 * 60 * 60 * 1000);
  
  schedule.scheduleJob(twoDaysBefore, async () => {
    try {
      const user = await client.users.fetch(userId);
      const warningEmbed = new MessageEmbed()
        .setTitle('Oryn Store | تنبيه انتهاء الاشتراك')
        .setColor('#355a5d')
        .setDescription(`
          **تنبيه:** اشتراكك على وشك الانتهاء!
          
          **الرمز:** ${code}
          **تاريخ الانتهاء:** ${new Date(expirationTime).toLocaleDateString()}
          **العدد:** ${count} bots
          **السيرفر:** ${serverId}
          
          يرجى تجديد اشتراكك قبل انتهاء المدة لتجنب تعطيل الخدمة.
          **Oryn Store**
        `)
        .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410839650366/Oryn_9.png?ex=683d61f2&is=683c1072&hm=c4cbe47f27039b844f24d50c5c9923adca04d9ca195807f60761c4138b901315&=&format=webp&quality=lossless&width=864&height=486')
        .setFooter({ text: 'للتجديد أو الاستفسار، يرجى التواصل مع الدعم الفني' });

      await user.send({ embeds: [warningEmbed] });
      
      // Update logs to mark as notified
      updateNotificationStatus(code, true);
    } catch (error) {
      console.error('فشل في إرسال تنبيه انتهاء الاشتراك:', error);
    }
  });

  // Schedule expiration notification
  const expirationDate = new Date(expirationTime);
  
  schedule.scheduleJob(expirationDate, async () => {
    try {
      const user = await client.users.fetch(userId);
      const expiredEmbed = new MessageEmbed()
        .setTitle('Oryn Store | انتهاء الاشتراك')
        .setColor('#355a5d')
        .setDescription(`
          **إشعار:** لقد انتهى اشتراكك!
          
          **الرمز:** ${code}
          **تاريخ الانتهاء:** ${new Date(expirationTime).toLocaleDateString()}
          **العدد:** ${count} bots
          **السيرفر:** ${serverId}
          
          تم إيقاف الخدمة بسبب انتهاء مدة الاشتراك.
          **Oryn Store**
        `)
        .setThumbnail('https://media.discordapp.net/attachments/1362373235458445605/1377384410839650366/Oryn_9.png?ex=683d61f2&is=683c1072&hm=c4cbe47f27039b844f24d50c5c9923adca04d9ca195807f60761c4138b901315&=&format=webp&quality=lossless&width=864&height=486')
        .setFooter({ text: 'للتجديد أو الاستفسار، يرجى التواصل مع الدعم الفني' });

      await user.send({ 
        files: ['https://media.discordapp.net/attachments/1362373235458445605/1377384410839650366/Oryn_9.png?ex=683d61f2&is=683c1072&hm=c4cbe47f27039b844f24d50c5c9923adca04d9ca195807f60761c4138b901315&=&format=webp&quality=lossless&width=864&height=486'],
        embeds: [expiredEmbed] 
      });
      
      // Remove expired subscription
      removeExpiredSubscription(code);
    } catch (error) {
      console.error('فشل في إرسال إشعار انتهاء الاشتراك:', error);
    }
  });
}

function updateNotificationStatus(code, notified) {
  try {
    // التحقق من وجود الملف
    if (!fs.existsSync('./logs.json')) {
      console.error('❌> ملف logs.json غير موجود');
      return;
    }
    
    const logs = fs.readFileSync('./logs.json', 'utf8');
    let logsArray = [];
    
    // التحقق من أن المحتوى صالح
    if (logs && logs.trim() !== '') {
      try {
        logsArray = JSON.parse(logs);
        if (!Array.isArray(logsArray)) {
          console.error('❌> محتوى ملف logs.json ليس مصفوفة');
          logsArray = [];
        }
      } catch (parseError) {
        console.error('❌> خطأ في تحليل ملف logs.json:', parseError);
        logsArray = [];
      }
    }
    
    // تحديث حالة الإشعار
    logsArray = logsArray.map(log => {
      if (log.code === code) {
        return { ...log, notified };
      }
      return log;
    });
    
    fs.writeFileSync('./logs.json', JSON.stringify(logsArray, null, 2));
    console.log(`تم تحديث حالة الإشعار للاشتراك: ${code}`);
  } catch (error) {
    console.error('حدث خطأ أثناء تحديث حالة الإشعار:', error);
  }
}

function removeExpiredSubscription(code) {
  try {
    // Remove from logs.json
    if (fs.existsSync('./logs.json')) {
      const logs = fs.readFileSync('./logs.json', 'utf8');
      let logsArray = [];
      try {
        logsArray = JSON.parse(logs);
        if (!Array.isArray(logsArray)) {
          logsArray = [];
        }
      } catch (parseError) {
        console.error('❌> خطأ في تحليل ملف logs.json:', parseError);
        logsArray = [];
      }
      
      logsArray = logsArray.filter(log => log.code !== code);
      fs.writeFileSync('./logs.json', JSON.stringify(logsArray, null, 2));
      console.log(`تم حذف الاشتراك المنتهي: ${code}`);
    }
    
    // Remove from tokens.json
    if (fs.existsSync('./tokens.json')) {
      const tokens = fs.readFileSync('./tokens.json', 'utf8');
      let tokensArray = [];
      try {
        tokensArray = JSON.parse(tokens);
        if (!Array.isArray(tokensArray)) {
          tokensArray = [];
        }
      } catch (parseError) {
        console.error('❌> خطأ في تحليل ملف tokens.json:', parseError);
        tokensArray = [];
      }
      
      tokensArray = tokensArray.filter(token => token.code !== code);
      fs.writeFileSync('./tokens.json', JSON.stringify(tokensArray, null, 2));
    }
  } catch (error) {
    console.error('حدث خطأ أثناء إزالة الاشتراك المنتهي:', error);
  }
}